# encoding: utf-8

import os
import time
from stock_similarity_analyzer import StockSimilarityAnalyzer

use_v3 = False # 是否使用V3版本

analyzer = StockSimilarityAnalyzer()
input_file = "股票列表.xlsx"
    
print("开始分析...")
result_df = analyzer.process_stock_list(input_file, use_v3)
    
# 保存为xlsx格式
timestamp = time.strftime('%Y%m%d_%H%M%S')
output_file = f"股票相似性分析结果_{timestamp}.xlsx"
result_df.to_excel(output_file, index=False)



