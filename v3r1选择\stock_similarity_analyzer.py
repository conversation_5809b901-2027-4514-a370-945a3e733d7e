# encoding: utf-8

import pandas as pd
from openai import OpenAI
import time
import json

class StockSimilarityAnalyzer:
    def __init__(self):
        self.key_db = "21125d99-0230-4988-9567-40c9bb6dacbd"
        self.client_db = OpenAI(
            api_key=self.key_db,
            base_url="https://ark.cn-beijing.volces.com/api/v3",
        )

    def get_response_deep_seek_db_r1(self, query: str, temperature: float = 0.7) -> str:
        try:
            completion = self.client_db.chat.completions.create(
                model="ep-20250205060440-7pvfl",
                messages=[
                    {"role": "user", "content": query},
                ],
                temperature=temperature,
            )
            return completion.choices[0].message.content
        except Exception as e:
            return str(e)

    def get_response_deep_seek_db_v3(self, query: str, temperature: float = 0.7) -> str:
        try:
            completion = self.client_db.chat.completions.create(
                model="ep-20250205060530-ltv4v",
                messages=[
                    {"role": "user", "content": query},
                ],
                temperature=temperature,
            )
            return completion.choices[0].message.content
        except Exception as e:
            print(e)
            return str(e)

    def create_similarity_query(self, stock_code: str, stock_name: str) -> str:
        query = f"""
请分析股票 {stock_code}({stock_name}) 的业务特点，并找出A股市场中与其业务最相似的一只股票。

要求：
1. 分析该股票的主营业务、行业特点、商业模式
2. 在A股市场中找出业务最相似的股票
3. 说明相似的具体原因（从业务模式、行业地位、产品结构等角度）

请按以下JSON格式返回结果：
{{
    "similar_stock": {{
        "code": "相似股票代码",
        "name": "相似股票简称"
    }},
    "similarity_reason": "详细的相似原因说明",
    "confidence": "相似度评分(1-10分)"
}}

请确保返回的是有效的JSON格式，股票代码必须带有.SH或.SZ。
"""
        return query

    def parse_api_response(self, response: str) -> dict:
        try:
            if response.strip().startswith('{'):
                return json.loads(response)

            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1

            if start_idx != -1 and end_idx != 0:
                json_str = response[start_idx:end_idx]
                return json.loads(json_str)

            return {
                "similar_stock": {"code": "未知", "name": "解析失败"},
                "similarity_reason": response,
                "confidence": "0"
            }

        except Exception as e:
            return {
                "similar_stock": {"code": "错误", "name": "解析异常"},
                "similarity_reason": f"解析错误: {str(e)}",
                "confidence": "0"
            }

    def analyze_single_stock(self, stock_code: str, stock_name: str, use_v3: bool = False) -> dict:
        query = self.create_similarity_query(stock_code, stock_name)

        for attempt in range(3):
            try:
                print(f"正在分析: {stock_code}({stock_name})")

                if use_v3:
                    response = self.get_response_deep_seek_db_v3(query, temperature=0.3)
                else:
                    response = self.get_response_deep_seek_db_r1(query, temperature=0.3)
                result = self.parse_api_response(response)

                if (result.get("similar_stock", {}).get("code") not in ["未知", "错误", "分析失败", None] and
                    result.get("similarity_reason") and
                    len(result.get("similarity_reason", "")) > 10):

                    print(f"分析完成: {result['similar_stock']['name']} (评分: {result['confidence']})")
                    print(f"分析原因: {result['similarity_reason']}")
                    return result

            except Exception as e:
                print(f"分析失败: {e}")

            if attempt < 2:
                time.sleep(1)

        return {
            "similar_stock": {"code": "分析失败", "name": "重试次数超限"},
            "similarity_reason": "API调用失败或解析错误",
            "confidence": "0"
        }

    def process_stock_list(self, input_file: str, use_v3: bool = False) -> pd.DataFrame:
        df = pd.read_excel(input_file, dtype=str)

        # 标准化列名
        column_mapping = {
            '代码': '股票代码', 'code': '股票代码', 'Code': '股票代码', 'CODE': '股票代码',
            '名称': '股票名称', 'name': '股票名称', 'Name': '股票名称', '简称': '股票名称', 'NAME': '股票名称'
        }

        for old_name, new_name in column_mapping.items():
            if old_name in df.columns:
                df = df.rename(columns={old_name: new_name})

        # 初始化结果列
        df['相似股票代码'] = ''
        df['相似股票简称'] = ''
        df['相似度评分'] = ''
        df['相似原因'] = ''

        total_stocks = len(df)
        for index, row in df.iterrows():
            stock_code = str(row['股票代码']).strip()
            stock_name = str(row['股票名称']).strip()

            print(f"进度: {index+1}/{total_stocks}")

            result = self.analyze_single_stock(stock_code, stock_name, use_v3)

            df.at[index, '相似股票代码'] = result.get('similar_stock', {}).get('code', '')
            df.at[index, '相似股票简称'] = result.get('similar_stock', {}).get('name', '')
            df.at[index, '相似度评分'] = result.get('confidence', '')
            df.at[index, '相似原因'] = result.get('similarity_reason', '')

            # time.sleep(0.5)

        return df
